﻿using BlueTape.EmailSender;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Builders;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Extensions;
using BlueTape.PaymentService.Application.Helpers;
using BlueTape.PaymentService.Application.Options;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Invoice;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.SNS.SlackNotification.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Services.Notification;

public class EmailNotificationService(
    IConfiguration configuration,
    INotificationReceiversService notificationReceiversService,
    ILoggerFactory loggerFactory,
    IOptions<TwilioTemplateOptions> templateOptions) : IEmailNotificationService
{
    private readonly ILogger _logger = loggerFactory.CreateLogger<EmailNotificationService>();

    private readonly SendGridEmailOptions _sendGridEmailOptions = new()
    {
        FromEmail = "<EMAIL>",
        FromName = "BlueTape Inc"
    };
    private readonly TwilioTemplateOptions _templateOptions = templateOptions.Value;

    public async Task<NotificationChannelDto<EmailPayloadDto>?> CustomerHaveToChooseDifferentPaymentMethod_V2(
        string payerId,
        string merchantName,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var subject = "Failed Payment Notification";

        var customerEmailAddresses = await notificationReceiversService
            .GetCompanyNotificationReceivers(payerId, ct);

        var url = UrlBuilder.GetApiUrl() + "/login";
        var htmlContent = $"<p>BlueTape: Your payment to {merchantName} has failed. Please click here to choose a different payment method and make the payment again {url}</p>";

        return new NotificationChannelDto<EmailPayloadDto>()
        {
            Payload = new EmailPayloadDto
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = customerEmailAddresses.Select(x => new EmailReceiverDataDto()
                {
                    Email = x.Email,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifySupplierTeamToContactWithCustomer_V2(
        string payerCompanyName,
        string mongoMerchantCompanyId,
        string mongoCustomerId,
        string takenById,
        string invoiceNumber,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var subject = "Customer Payment Failed";

        var supplierEmailAddresses = await notificationReceiversService
            .GetSupplierNotificationReceivers(
                mongoCustomerId,
                takenById,
                mongoMerchantCompanyId,
                ct);

        var url = UrlBuilder.GetApiUrl() + "/login";
        var htmlContent = $"<p>{payerCompanyName} payment for invoice {invoiceNumber} has failed. Please contact customer for other payment options. Login to see the details {url}</p>";

        return new NotificationChannelDto<EmailPayloadDto>()
        {
            Payload = new EmailPayloadDto
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = supplierEmailAddresses.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifySupplierTeamAboutErrorDuringMerchantPushTransaction_V2(
        string payerCompanyName,
        string mongoMerchantCompanyId,
        string mongoCustomerId,
        string takenById,
        string invoiceNumber,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var subject = "Customer Payment Failed";

        _logger.LogInformation("Sending notification to supplier");

        var supplierEmailAddresses = await notificationReceiversService
            .GetSupplierNotificationReceivers(
                mongoCustomerId,
                takenById,
                mongoMerchantCompanyId,
                ct);

        var url = UrlBuilder.GetApiUrl() + "/login";
        var htmlContent = $"<p>{payerCompanyName} payment for invoice {invoiceNumber} has failed during merchant push transaction. Login to see the details {url}</p>";

        return new NotificationChannelDto<EmailPayloadDto>()
        {
            Payload = new EmailPayloadDto
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = supplierEmailAddresses.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutFailedInternalTransferTransaction_V2(
        string merchantCompanyName,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var subject = "Failed Payment Notification";
        var htmlContent = $"<p>BlueTape: Internal transaction of payment to {merchantCompanyName} has failed</p>";

        return new NotificationChannelDto<BlueTapeBackOfficePayloadDto>()
        {
            Payload = new BlueTapeBackOfficePayloadDto()
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                SlackNotification = new SlackPayloadDto
                {
                    EventLevel = EventLevel.Error,
                    EventName = "FailedInternalTransfer",
                    Message = $"BlueTape: Internal transaction of payment to {merchantCompanyName} has failed. PaymentRequestId: {paymentRequestId}, TransactionHistoryId: {transactionHistoryId}",
                }
            }
        };
    }

    public async Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutRecalled_V2(
        string[] invoiceNumbers,
        decimal paymentRequestAmount,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var subject = "ACH Pull was returned";

        var numbers = string.Join(", ", invoiceNumbers);
        var htmlContent = $"<p>The ACH Pull for invoices {numbers} with total amount {paymentRequestAmount.ToDollarString()} is returned.</p>";

        return new NotificationChannelDto<BlueTapeBackOfficePayloadDto>()
        {
            Payload = new BlueTapeBackOfficePayloadDto
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                SlackNotification = new SlackPayloadDto
                {
                    EventLevel = EventLevel.Warning,
                    EventName = "AchReturn",
                    Message = $"The ACH Pull for invoices {numbers} with total amount {paymentRequestAmount.ToDollarString()} is returned. PaymentRequestId: {paymentRequestId}, TransactionHistoryId: {transactionHistoryId}",
                }
            }
        };
    }

    public async Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutPaymentFailed_V2(
        string[] invoiceNumbers,
        decimal paymentRequestAmount,
        string errorReason,
        string transactionStatus,
        Guid paymentRequestId,
        string aionReferenceNumber,
        Guid? drawId,
        string? customerName,
        string? loanApplicationLink,
        PaymentRequestType paymentRequestType,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        var invoicesSubject = string.Join(", ", invoiceNumbers);
        var subject = $"Alert! Funds movement failure for Invoice(s): {invoicesSubject}";

        var invoicesDetails = string.Join(Environment.NewLine, invoiceNumbers.Select(invoiceNumber =>
            $"<p>Invoice ID: {invoiceNumber}<br>"));

        var htmlContent = $@"
        <p>A process in the Flow of Funds has failed:</p>
        <p>Status: {transactionStatus}</p>
        <p>Reason: {errorReason}</p>
        <p>Type: {paymentRequestType}</p>
        <p>Aion reference number: {aionReferenceNumber}</p>
        <p>Loan ID: {drawId}</p>
        <p>Amount: {paymentRequestAmount.ToDollarString()}</p>
        <p>Customer Name: {customerName}</p>
        <p>Details for failed invoices:</p>
        {invoicesDetails}
        <p>{loanApplicationLink}</p>";

        var slackContent = $@"
        A process in the Flow of Funds has failed:
        Status: {transactionStatus}
        Reason: {errorReason}
        Type: {paymentRequestType}
        Aion reference number: {aionReferenceNumber}
        Loan ID: {drawId}
        Amount: {paymentRequestAmount.ToDollarString()}
        Customer Name: {customerName}
        Details for failed invoices:
        {invoicesDetails}
        {loanApplicationLink}";

        return new NotificationChannelDto<BlueTapeBackOfficePayloadDto>()
        {
            Payload = new BlueTapeBackOfficePayloadDto
            {
                Html = htmlContent,
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                SlackNotification = new SlackPayloadDto
                {
                    EventLevel = EventLevel.Warning,
                    EventName = "AchReturn",
                    Message = slackContent
                }
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentIsProcessing(
    IEnumerable<InvoiceEntity> invoices,
    string customerAccountName,
    string bankAccountNumber,
    decimal paymentRequestAmount,
    decimal taxedAmount,
    string payerId,
    CancellationToken ct)
    {
        invoices = invoices.ToArray();
        var notificationName = EmailTemplateName.IhcRepaymentProcessing.ToString();

        _logger.LogInformation("Getting template for {1} email notification", notificationName);
        _templateOptions.Templates.TryGetValue(EmailTemplateName.IhcRepaymentProcessing, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for {1}", notificationName);
            return null;
        }

        _logger.LogInformation("Getting email receivers");
        var receiverEmails = new List<string>();

        var receivers = await notificationReceiversService
            .GetCompanyNotificationReceivers(payerId, ct);

        receiverEmails.AddRange(receivers.Select(x => x.Email));

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("IHC repayment Is Processed selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var invoiceNumbers = invoices.Select(x => x.InvoiceNumber).ToList();

        var subject = $"Ihc repayment is processed for {paymentRequestAmount.ToDollarString()}";

        var dynamicEmailData = new
        {
            customerName = receivers.FirstOrDefault()?.FirstName,
            invoiceNumber = string.Join(", ", invoiceNumbers),
            paymentDate = DateTime.Now.ToString("MM/dd/yyyy"),
            amountCharged = paymentRequestAmount.ToDollarString(),
            processingFee = (taxedAmount - paymentRequestAmount).ToDollarString(),
            totalCharged = taxedAmount.ToDollarString(),
            paymentMethod = $"Bank {bankAccountNumber}"
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentFailed(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        string bankAccountNumber,
        decimal paymentRequestAmount,
        string failureReason,
        string payerId,
        CancellationToken ct)
    {
        var notificationType = EmailTemplateName.IhcRepaymentFailed;
        invoices = invoices.ToArray();
        var notificationName = notificationType.ToString();

        _logger.LogInformation("Getting template for {1} email notification", notificationName);
        _templateOptions.Templates.TryGetValue(notificationType, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for {1}", notificationName);
            return null;
        }

        _logger.LogInformation("Getting email receivers");
        var receiverEmails = new List<string>();

        var receivers = await notificationReceiversService
            .GetCompanyNotificationReceivers(payerId, ct);

        receiverEmails.AddRange(receivers.Select(x => x.Email));

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("IHC repayment Is Failed selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var invoiceNumbers = invoices.Select(x => x.InvoiceNumber).ToList();
        var subject = $"Ihc repayment with amount {paymentRequestAmount.ToDollarString()} is failed";

        var dynamicEmailData = new
        {
            customerName = receivers.FirstOrDefault()?.FirstName,
            invoiceNumber = string.Join(", ", invoiceNumbers),
            paymentDate = DateTime.Now.ToString("MM/dd/yyyy"),
            amount = paymentRequestAmount.ToDollarString(),
            paymentMethod = $"Bank {bankAccountNumber}",
            failureReason = failureReason,
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentFailedToOpsTeam(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        string customerEmail,
        string failureReason,
        CancellationToken ct)
    {
        var notificationType = EmailTemplateName.IhcRepaymentFailedToOpsTeam;
        invoices = invoices.ToArray();
        var notificationName = notificationType.ToString();

        _logger.LogInformation("Getting template for {1} email notification", notificationName);
        _templateOptions.Templates.TryGetValue(notificationType, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for {1}", notificationName);
            return null;
        }

        _logger.LogInformation("Getting email receivers");
        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await AddReceivers(invoice, NotificationReceiverType.OperationTeam, ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("IHC repayment Is Failed selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var invoiceNumbers = invoices.Select(x => x.InvoiceNumber).ToList();
        var subject = $"Ihc repayment with amount {invoices.Sum(x => x.TotalAmount).ToDollarString()} is failed";

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            customerEmail = customerEmail,
            invoiceNumber = string.Join(", ", invoiceNumbers),
            invoiceAmount = paymentRequestAmount.ToDollarString(),
            failureReason = failureReason,
            failureDate = DateTime.Now.ToString("MM/dd/yyyy"),
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyPaymentIsProcessed_V2(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        decimal taxedAmount,
        Guid paymentRequestId,
        PaymentRequestType paymentRequestType,
        string paymentType,
        Guid transactionId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        invoices = invoices.ToArray();

        _logger.LogInformation("Getting template for payment processed's email notification");
        _templateOptions.Templates.TryGetValue(EmailTemplateName.PaymentRequestProcessed, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        _logger.LogInformation("Getting email receivers");
        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await notificationReceiversService
                .GetSupplierNotificationReceivers(
                    invoice.CustomerAccountId,
                    invoice.TakenById,
                    invoice.CompanyId,
                    ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("Payment Is Processed selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var labelProcessed = "Amount processed";

        string subject;
        var invoiceNumbers = invoices.Select(x => x.InvoiceNumber).ToList();

        if (paymentRequestType is PaymentRequestType.InvoicePayment or PaymentRequestType.InvoicePaymentV2)
        {
            subject = invoiceNumbers.Count() > 1 ? $"Invoices is processed for {paymentRequestAmount.ToDollarString()}" :
                $"Invoice {invoiceNumbers[0]} is processed for {paymentRequestAmount.ToDollarString()}";
        }
        else
        {
            subject = $"Advance payment is processed for {paymentRequestAmount.ToDollarString()}";
        }

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            invoiceNumber = string.Join(", ", invoiceNumbers),
            amount = invoices.Sum(x => x.TotalAmount).ToDollarString(),
            paymentType,
            transactionNumber = transactionId,
            amountProcessed = taxedAmount.ToDollarString(),
            labelProcessed,
            subject
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = subject,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyAdvancePaymentIsProcessing_V2(
        PaymentTransactionEntity transaction,
        Guid transactionHistoryId,
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        CancellationToken ct)
    {
        _logger.LogInformation("Getting template for advance payment processing email notification");
        _templateOptions.Templates.TryGetValue(EmailTemplateName.AdvancePaymentProcessing, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        var paymentRequest = transaction.PaymentRequest;
        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await notificationReceiversService
                .GetSupplierNotificationReceivers(
                    invoice.CustomerAccountId,
                    invoice.TakenById,
                    invoice.CompanyId,
                    ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("Advance payment Is Processing selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            totalInvoiceAmount = invoices.Sum(x => x.TotalAmount).ToDollarString(),
            typeOfPayment = DisplayHelper.GetPaymentType(paymentRequest.RequestType),
            totalAmountProcessing = paymentRequest.Amount.ToDollarString(),
            totalAdvancePayment = paymentRequest.Amount.ToDollarString(),
            transactionId = transaction.ReferenceNumber,
            invoices = invoices.Select(x => new
            {
                number = x.InvoiceNumber,
                amount = x.TotalAmount.ToDollarString()
            })
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = string.Empty,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyFinalPaymentIsProcessing_V2(
        PaymentTransactionEntity transaction,
        Guid transactionHistoryId,
        IEnumerable<InvoiceEntity> invoices,
        decimal merchantFee,
        string customerAccountName,
        CancellationToken ct)
    {
        _logger.LogInformation("Getting template for final payment processing email notification");
        _templateOptions.Templates.TryGetValue(EmailTemplateName.FinalPaymentProcessing, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        var paymentRequest = transaction.PaymentRequest;
        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await notificationReceiversService
                .GetSupplierNotificationReceivers(
                    invoice.CustomerAccountId,
                    invoice.TakenById,
                    invoice.CompanyId,
                    ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("Advance payment Is Processing selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            totalInvoiceAmount = invoices.Sum(x => x.TotalAmount).ToDollarString(),
            merchantFee = merchantFee.ToDollarString(),
            typeOfPayment = DisplayHelper.GetPaymentType(paymentRequest.RequestType),
            totalAmountProcessing = paymentRequest.Amount.ToDollarString(),
            totalAdvancePayment = paymentRequest.Amount.ToDollarString(),
            transactionId = transaction.ReferenceNumber,
            invoices = invoices.Select(x => new
            {
                number = x.InvoiceNumber,
                amount = x.TotalAmount.ToDollarString()
            })
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = string.Empty,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyAdvancePaymentIsProcessed_V2(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        Guid paymentRequestId,
        PaymentRequestType paymentRequestType,
        string paymentType,
        Guid transactionId,
        Guid transactionHistoryId,
        CancellationToken ct)
    {
        _logger.LogInformation("Getting template for advance payment processed's email notification");
        _templateOptions.Templates.TryGetValue(EmailTemplateName.MultipleAdvanceProcessed, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        _logger.LogInformation("Getting email receivers");
        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await notificationReceiversService
                .GetSupplierNotificationReceivers(
                    invoice.CustomerAccountId,
                    invoice.TakenById,
                    invoice.CompanyId,
                    ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("Payment Is Processed selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        if (receiverEmails == null || !receiverEmails.Any()) return null;
        var labelProcessed = "Amount processed";

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            totalInvoiceAmount = invoices.Sum(x => x.TotalAmount).ToDollarString(),
            typeOfPayment = paymentType,
            totalAmountProcessed = paymentRequestAmount.ToDollarString(),
            totalAdvancePayment = paymentRequestAmount.ToDollarString(),
            transactionId = transactionId,
            invoices = invoices.Select(x => new
            {
                number = x.InvoiceNumber,
                amount = x.TotalAmount.ToDollarString()
            })
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = string.Empty,
                From = new EmailReceiverDataDto()
                {
                    Email = templateDetails?.FromEmail ?? _sendGridEmailOptions.FromEmail,
                    Name = templateDetails?.FromName ?? _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyInvoiceIsProcessing_V2(
        InvoiceEntity invoice,
        string customerAccountName,
        Guid paymentRequestId,
        Guid? transactionHistoryId,
        NotificationReceiverType receiver,
        CancellationToken ct)
    {
        var receiverEmails = await AddReceivers(invoice, receiver, ct);
        var subject = "Customer Payment Processing";
        var url = UrlBuilder.GetApiUrl() + "/login";
        var htmlContent = $"<p>{customerAccountName} has made a payment on {invoice.InvoiceNumber}, the payment is processing. Login to see the status {url}</p>";

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = string.Empty,
                TemplatePayload = string.Empty,
                Subject = subject,
                Html = htmlContent,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyFinalPaymentIsSuccess_V2(
        IEnumerable<InvoiceEntity>? invoices,
        string customerAccountName,
        Guid paymentRequestId,
        decimal paymentRequestAmount,
        string paymentType,
        Guid transactionHistoryId,
        string transactionNumber,
        decimal merchantFee,
        CancellationToken ct)
    {
        _templateOptions.Templates.TryGetValue(EmailTemplateName.MultipleFinalPayment, out var templateDetails);
        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        _logger.LogInformation("Sending final payment is success email");

        var receiverEmails = new List<string>();

        foreach (var invoice in invoices)
        {
            var emails = await notificationReceiversService
                .GetSupplierNotificationReceivers(
                    invoice.CustomerAccountId,
                    invoice.TakenById,
                    invoice.CompanyId,
                    ct);

            receiverEmails.AddRange(emails);
        }

        receiverEmails = receiverEmails.Distinct().ToList();
        _logger.LogInformation("Final payment selected email receivers: {receivers}", string.Join(", ", receiverEmails));

        var labelProcessed = "Final payment";
        var subject = $"Final payment is processed for {paymentRequestAmount.ToDollarString()}";

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            labelProcessed = labelProcessed,
            subject = subject,
            totalInvoiceAmount = invoices.Sum(x => x.TotalAmount).ToDollarString(),
            merchantFee = merchantFee.ToDollarString(),
            totalFinalPayment = paymentRequestAmount.ToDollarString(),
            typeOfPayment = paymentType,
            transactionId = transactionNumber,
            totalAmountProcessed = paymentRequestAmount.ToDollarString(),
            invoices = invoices.Select(x => new
            {
                number = x.InvoiceNumber,
                amount = x.TotalAmount.ToDollarString()
            })
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = subject,
                Html = string.Empty,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    public async Task<NotificationChannelDto<EmailPayloadDto>?> NotifyThatPaymentIsMade_V2(
        InvoiceEntity invoice,
        string customerAccountName,
        Guid paymentRequestId,
        decimal paymentRequestAmount,
        CancellationToken ct)
    {
        _logger.LogInformation("Getting template for payment processing email notification");
        _templateOptions.Templates.TryGetValue(EmailTemplateName.PaymentRequestProcessing, out var templateDetails);

        if (templateDetails is null)
        {
            _logger.LogError("Unable to find template for NotifyThatPaymentIsMade");
            return null;
        }

        _logger.LogInformation("Getting email receivers");

        var receiverEmails = await notificationReceiversService
            .GetSupplierNotificationReceivers(
                invoice.CustomerAccountId,
                invoice.TakenById,
                invoice.CompanyId,
                ct);

        if (!receiverEmails.Any()) return null;

        var dynamicEmailData = new
        {
            customerName = customerAccountName,
            invoiceNumber = invoice.InvoiceNumber,
            amount = invoice.TotalAmount.ToDollarString(),
            totalProcessingAmount = paymentRequestAmount.ToDollarString(),
            paymentType = "ACH"
        };

        return new NotificationChannelDto<EmailPayloadDto>
        {
            Payload = new EmailPayloadDto
            {
                TemplateId = templateDetails?.TemplateId,
                TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                Subject = string.Empty,
                Html = string.Empty,
                From = new EmailReceiverDataDto()
                {
                    Email = _sendGridEmailOptions.FromEmail,
                    Name = _sendGridEmailOptions.FromName
                },
                Receivers = receiverEmails.Select(x => new EmailReceiverDataDto()
                {
                    Email = x,
                    Name = string.Empty
                }).ToList(),
            }
        };
    }

    private async Task<List<string>> AddReceivers(InvoiceEntity? invoice, NotificationReceiverType receiver, CancellationToken ct)
    {
        var emails = new List<string>();

        if (invoice?.CompanyId is not null && receiver is
                NotificationReceiverType.MerchantAndCustomer or NotificationReceiverType.Merchant)
        {
            _logger.LogInformation("Sending notification to supplier");
            var supplierEmailAddresses = await notificationReceiversService.GetSupplierNotificationReceivers(invoice.CustomerAccountId,
                invoice.TakenById, invoice.CompanyId, ct);
            foreach (var email in supplierEmailAddresses)
            {
                _logger.LogInformation("Email receiver: {email}", email);
                emails.Add(email);
            }
        }
        if (invoice?.PayerId is not null && receiver is NotificationReceiverType.MerchantAndCustomer)
        {
            _logger.LogInformation("Sending notification to customer");
            var receivers = await notificationReceiversService.GetCompanyNotificationReceivers(invoice.PayerId, ct);
            foreach (var companyReceiver in receivers)
            {
                _logger.LogInformation("Email receiver: {email}", companyReceiver.Email);
                emails.Add(companyReceiver.Email);
            }
        }
        if (receiver is NotificationReceiverType.OperationTeam)
        {
            foreach (var email in GetOpsTeamEmails())
            {
                _logger.LogInformation("Email receiver: {email}", email);
                emails.Add(email);
            }
        }

        return emails;
    }

    private List<string> GetOpsTeamEmails()
    {
        var email = configuration[KeyVaultKeysConstants.OperationTeamEmail];

        if (!string.IsNullOrWhiteSpace(email))
        {
            var emails = email.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(e => e.Trim()).ToList();

            return emails;
        }

        return [];
    }
}
