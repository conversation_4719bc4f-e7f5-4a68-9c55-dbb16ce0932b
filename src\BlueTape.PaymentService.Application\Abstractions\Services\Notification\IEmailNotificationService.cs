﻿using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Invoice;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Abstractions.Services.Notification;

public interface IEmailNotificationService
{
    Task<NotificationChannelDto<EmailPayloadDto>?> CustomerHaveToChooseDifferentPaymentMethod_V2(
        string payerId,
        string merchantName,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifySupplierTeamToContactWithCustomer_V2(
        string payerCompanyName,
        string mongoMerchantCompanyId,
        string mongoCustomerId,
        string takenById,
        string invoiceNumber,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifySupplierTeamAboutErrorDuringMerchantPushTransaction_V2(
        string payerCompanyName,
        string mongoMerchantCompanyId,
        string mongoCustomerId,
        string takenById,
        string invoiceNumber,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutFailedInternalTransferTransaction_V2(
        string merchantCompanyName,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutRecalled_V2(
        string[] invoiceNumbers,
        decimal paymentRequestAmount,
        Guid paymentRequestId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<BlueTapeBackOfficePayloadDto>?> NotifyOpsTeamAboutPaymentFailed_V2(
        string[] invoiceNumbers,
        decimal paymentRequestAmount,
        string errorReason,
        string transactionStatus,
        Guid paymentRequestId,
        string aionReferenceNumber,
        Guid? drawId,
        string? customerName,
        string? loanApplicationLink,
        PaymentRequestType paymentRequestType,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyPaymentIsProcessed_V2(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        decimal taxedAmount,
        Guid paymentRequestId,
        PaymentRequestType paymentRequestType,
        string paymentType,
        Guid transactionId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyAdvancePaymentIsProcessed_V2(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        Guid paymentRequestId,
        PaymentRequestType paymentRequestType,
        string paymentType,
        Guid transactionId,
        Guid transactionHistoryId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyFinalPaymentIsSuccess_V2(
        IEnumerable<InvoiceEntity>? invoices,
        string customerAccountName,
        Guid paymentRequestId,
        decimal paymentRequestAmount,
        string paymentType,
        Guid transactionHistoryId,
        string transactionNumber,
        decimal merchantFee,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyInvoiceIsProcessing_V2(
        InvoiceEntity invoice,
        string customerAccountName,
        Guid paymentRequestId,
        Guid? transactionHistoryId,
        NotificationReceiverType receiver,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyThatPaymentIsMade_V2(
        InvoiceEntity invoice,
        string customerAccountName,
        Guid paymentRequestId,
        decimal paymentRequestAmount,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyAdvancePaymentIsProcessing_V2(
        PaymentTransactionEntity transaction,
        Guid transactionHistoryId,
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyFinalPaymentIsProcessing_V2(
        PaymentTransactionEntity transaction,
        Guid transactionHistoryId,
        IEnumerable<InvoiceEntity> invoices,
        decimal merchantFee,
        string customerAccountName,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentIsProcessing(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        string bankAccountNumber,
        decimal paymentRequestAmount,
        decimal taxedAmount,
        string payerId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentFailed(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        string bankAccountNumber,
        decimal paymentRequestAmount,
        string failureReason,
        string payerId,
        CancellationToken ct);

    Task<NotificationChannelDto<EmailPayloadDto>?> NotifyIhcRepaymentFailedToOpsTeam(
        IEnumerable<InvoiceEntity> invoices,
        string customerAccountName,
        decimal paymentRequestAmount,
        string customerEmail,
        string failureReason,
        CancellationToken ct);
}
