using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;

public interface ISubscriptionFeeTransactionSyncService
{
    Task PerformTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken);
    Task SyncTransactions(PaymentRequestEntity paymentRequest, List<string> operationIds, CancellationToken cancellationToken);
}
