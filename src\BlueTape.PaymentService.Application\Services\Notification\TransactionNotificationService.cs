﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.Enums;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Extensions;
using BlueTape.PaymentService.Application.Helpers;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.Utilities.Constants;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services.Notification;

public class TransactionNotificationService(
    ICompanyHttpClient companyHttpClient,
    IInvoiceRepository invoiceRepository,
    IEmailNotificationService emailNotificationService,
    ICustomerRepository customerAccountsRepository,
    IPaymentRequestRepository paymentRequestRepository,
    IDatabaseNotificationService databaseNotificationService,
    ILoanApplicationRepository loanApplicationRepository,
    IOperationsService operationsService,
    IAzureNotificationSenderService azureNotificationSenderService,
    ITraceIdAccessor traceIdAccessor,
    ILoggerFactory loggerFactory) : ITransactionNotificationService
{
    private readonly ILogger _logger = loggerFactory.CreateLogger<TransactionNotificationService>();

    public async Task HandleInvoicePaymentRequestCreated(Guid paymentRequestId, CancellationToken ct)
    {
        var paymentRequest = await paymentRequestRepository.GetById(paymentRequestId, ct);
        if (paymentRequest == null)
        {
            throw new PaymentRequestDoesNotExistException($"Payment request does not exist for id: {paymentRequestId}");
        }

        foreach (var payable in paymentRequest.PaymentRequestPayables)
        {
            var invoice = await invoiceRepository.GetById(payable.PayableId, ct);
            if (invoice is null) throw new PaymentValidationException($"Unable to find invoice by id: {payable.PayableId}");

            var customerAccountName = !string.IsNullOrEmpty(invoice.CustomerAccountId)
                ? (await customerAccountsRepository.GetByIdAsync(invoice.CustomerAccountId, ct))?.Name
                : string.Empty;

            var notifications = new List<SystemNotificationDto>();

            var referenceIds = new List<string> { invoice.Id.ToString(), paymentRequest.Id.ToString() };
            if (!string.IsNullOrEmpty(invoice.CustomerAccountId)) referenceIds.Add(invoice.CustomerAccountId);
            var notificationName = "InvoicePaymentRequestCreated";
            var notificationDescription = $"Invoice payment started on {invoice.InvoiceNumber}";

            var paymentMadeEmail = await emailNotificationService.NotifyThatPaymentIsMade_V2
                (invoice, customerAccountName, paymentRequestId, paymentRequest.Amount, ct);

            if (paymentMadeEmail != null)
            {
                var customerNotification = GetNotification(notificationName, notificationDescription, referenceIds, paymentRequest.PayerId);
                paymentMadeEmail.Payload.SenderCompanyId = paymentRequest.PayeeId;
                customerNotification.EmailDelivery!.Add(paymentMadeEmail);
                customerNotification.UserUiReviewDelivery =
                [
                    new()
                    {
                        Payload = new UserUiReviewPayloadDto
                        {
                            NotificationLevel = NotificationLevel.Info,
                            Description = $"Invoice payment started on {invoice.InvoiceNumber}"
                        }
                    }
                ];

                notifications.Add(customerNotification);
            }

            var notifyInvoiceIsProcessing = await emailNotificationService
                .NotifyInvoiceIsProcessing_V2(invoice, customerAccountName, paymentRequestId, null, NotificationReceiverType.Merchant, ct);

            if (notifyInvoiceIsProcessing != null)
            {
                var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, paymentRequest.PayeeId);
                notifyInvoiceIsProcessing.Payload.SenderCompanyId = paymentRequest.PayeeId;
                supplierNotification.EmailDelivery!.Add(notifyInvoiceIsProcessing);
                supplierNotification.UserUiReviewDelivery =
                [
                    new()
                    {
                        Payload = new UserUiReviewPayloadDto
                        {
                            NotificationLevel = NotificationLevel.Info,
                            Description = $"{customerAccountName} has made a payment on {invoice.InvoiceNumber}, the payment is processing"
                        }
                    }
                ];

                notifications.Add(supplierNotification);
            }

            if (notifications.Any()) await azureNotificationSenderService.Send(notifications, ct);

            await databaseNotificationService.CreateNotificationInMongo(paymentRequest.PayeeId, paymentRequest.PayerId, payable.PayableId, ct);
            await databaseNotificationService.MakeInvoiceNotificationRead(payable.PayableId, paymentRequest.PayerId, ct);
        }
    }

    private SystemNotificationDto GetNotification(
        string notificationName, string notificationDescription, List<string> referenceIds, string? notificationReceiverCompanyId)
    {
        var systemNotification = new SystemNotificationDto
        {
            Source = NotificationSource.Payment,
            TraceId = traceIdAccessor.TraceId,
            CompanyId = notificationReceiverCompanyId,
            NotificationName = notificationName,
            Description = notificationDescription,
            ReferenceIds = referenceIds,
            EmailDelivery = new List<NotificationChannelDto<EmailPayloadDto>>(),
            UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>(),
            BlueTapeBackOfficeDelivery = new List<NotificationChannelDto<BlueTapeBackOfficePayloadDto>>()
        };

        return systemNotification;
    }

    public async Task HandleAdvancePaymentProcessing(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var paymentRequest = transaction.PaymentRequest;
        if (paymentRequest == null)
        {
            throw new PaymentRequestDoesNotExistException($"Payment request does not exist for transaction id: {transaction.Id}");
        }

        var payableIds = paymentRequest.PaymentRequestPayables.Select(x => x.PayableId).ToList();
        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        if (!invoices.Any()) throw new PaymentValidationException($"Unable to find invoice by ids: {string.Join(", ", payableIds)}");

        var customerAccountName = !string.IsNullOrEmpty(invoices[0].CustomerAccountId)
            ? (await customerAccountsRepository.GetByIdAsync(invoices[0].CustomerAccountId!, ct))?.Name
            : string.Empty;

        var advancePaymentIsProcessingV2 = await emailNotificationService
            .NotifyAdvancePaymentIsProcessing_V2(transaction, transactionHistoryId, invoices, customerAccountName, ct);

        if (advancePaymentIsProcessingV2 != null)
        {
            var numbers = string.Join(", ", invoices.Select(x => x.InvoiceNumber).ToArray());

            var notificationName = "AdvancePaymentProcessing";
            var notificationDescription = $"Advance payment started on {numbers}";

            var invoiceIds = invoices.Select(x => x.Id).Distinct().ToList();
            var customerIds = invoices.Select(x => x.CustomerAccountId).Where(x => !string.IsNullOrEmpty(x))
                .Distinct().ToList();

            var referenceIds = new List<string> { paymentRequest.Id.ToString() };
            if (invoiceIds.Any()) referenceIds.AddRange(invoiceIds);
            if (customerIds != null && customerIds.Any()) referenceIds.AddRange(customerIds!);

            var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, paymentRequest.PayeeId);
            advancePaymentIsProcessingV2.Payload.SenderCompanyId = paymentRequest.PayeeId;
            supplierNotification.EmailDelivery!.Add(advancePaymentIsProcessingV2);
            supplierNotification.UserUiReviewDelivery!.Add(new NotificationChannelDto<UserUiReviewPayloadDto>()
            {
                Payload = new UserUiReviewPayloadDto
                {
                    NotificationLevel = NotificationLevel.Info,
                    Description = $"Advance payment started on invoices: {numbers}"
                }
            });

            await azureNotificationSenderService.Send(supplierNotification, ct);
        }
    }

    public async Task HandleFinalPaymentProcessing(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var paymentRequest = transaction.PaymentRequest;
        if (paymentRequest == null)
        {
            throw new PaymentRequestDoesNotExistException($"Payment request does not exist for transaction id: {transaction.Id}");
        }

        var payableIds = paymentRequest.PaymentRequestPayables.Select(x => x.PayableId).ToList();
        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        if (!invoices.Any()) throw new PaymentValidationException($"Unable to find invoice by ids: {string.Join(", ", payableIds)}");

        var customerAccountName = !string.IsNullOrEmpty(invoices[0].CustomerAccountId)
            ? (await customerAccountsRepository.GetByIdAsync(invoices[0].CustomerAccountId!, ct))?.Name
            : string.Empty;

        var fee = await operationsService.GetTotalMerchantFee(invoices, ct);

        var finalPaymentIsProcessingV2 = await emailNotificationService
            .NotifyFinalPaymentIsProcessing_V2(transaction, transactionHistoryId, invoices, fee, customerAccountName, ct);

        if (finalPaymentIsProcessingV2 != null)
        {
            var numbers = string.Join(", ", invoices.Select(x => x.InvoiceNumber).ToArray());

            var notificationName = "FinalPaymentProcessing";
            var notificationDescription = $"Final payment processing on {numbers}";

            var invoiceIds = invoices.Select(x => x.Id).Distinct().ToList();
            var customerIds = invoices.Select(x => x.CustomerAccountId).Where(x => !string.IsNullOrEmpty(x))
                .Distinct().ToList();

            var referenceIds = new List<string> { paymentRequest.Id.ToString() };

            if (invoiceIds.Any()) referenceIds.AddRange(invoiceIds);
            if (customerIds != null && customerIds.Any()) referenceIds.AddRange(customerIds!);

            var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, paymentRequest.PayeeId);
            finalPaymentIsProcessingV2.Payload.SenderCompanyId = paymentRequest.PayeeId;
            supplierNotification.EmailDelivery!.Add(finalPaymentIsProcessingV2);
            supplierNotification.UserUiReviewDelivery.Add(new NotificationChannelDto<UserUiReviewPayloadDto>
            {
                Payload = new UserUiReviewPayloadDto
                {
                    NotificationLevel = NotificationLevel.Info,
                    Description = $"Final payment processing on {numbers}"
                }
            });

            await azureNotificationSenderService.Send(supplierNotification, ct);
        }
    }

    public async Task HandleInvoicePaymentFailed(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var allowedStatusCodeToNotify = new List<string> { "R01" };

        if (!allowedStatusCodeToNotify.Any(x => x.Equals(transaction.LastResultCode)))
        {
            _logger.LogInformation("For transactionId: {id} and LastResultCode: {LastResultCode} unable to send notification due to business requirements", transaction.Id, transaction.LastResultCode);
            return;
        }

        var receiver = TransactionHelper.GetNotificationReceiverType(transaction.SequenceNumber);

        var companies = await companyHttpClient.GetCompaniesByIdsAsync([transaction.PaymentRequest?.PayerId, transaction.PaymentRequest?.PayeeId], ct);

        if (companies.Count != 2) throw new Exception();

        var payerCompany = companies.First(x => x.Id == transaction.PaymentRequest?.PayerId);
        var merchantCompany = companies.First(x => x.Id == transaction.PaymentRequest?.PayeeId);

        foreach (var payable in transaction.PaymentRequest!.PaymentRequestPayables)
        {
            var invoice = await invoiceRepository.GetById(payable.PayableId, ct);
            if (invoice is null) throw new Exception($"Payable doest not exist: {payable.PayableId}");

            var notificationName = "InvoicePaymentFailed";
            var notificationDescription = $"Invoice payment failed on {invoice.InvoiceNumber}";

            var referenceIds = new List<string>
            {
                transaction.PaymentRequestId.ToString(),
                invoice.Id.ToString(),
            };

            if (!string.IsNullOrEmpty(invoice.CustomerAccountId))
            {
                referenceIds.Add(invoice.CustomerAccountId);
            }

            switch (receiver)
            {
                case NotificationReceiverType.MerchantAndCustomer:
                    var customerHaveToChooseDifferentPaymentMethod = await emailNotificationService
                        .CustomerHaveToChooseDifferentPaymentMethod_V2(
                        payerCompany.Id,
                        merchantCompany.Name,
                        transaction.PaymentRequestId,
                        transactionHistoryId,
                        ct);

                    if (customerHaveToChooseDifferentPaymentMethod != null)
                    {
                        var customerNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
                        customerHaveToChooseDifferentPaymentMethod.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
                        customerNotification.EmailDelivery!.Add(customerHaveToChooseDifferentPaymentMethod);
                        customerNotification.UserUiReviewDelivery!.Add(new NotificationChannelDto<UserUiReviewPayloadDto>
                        {
                            Payload = new UserUiReviewPayloadDto
                            {
                                NotificationLevel = NotificationLevel.Info,
                                Description = notificationDescription
                            }
                        });

                        await azureNotificationSenderService.Send(customerNotification, ct);
                    }

                    var supplierTeamToContactWithCustomerV2 = await emailNotificationService.NotifySupplierTeamToContactWithCustomer_V2(
                        payerCompany.Name,
                        merchantCompany.Id,
                        invoice.CustomerAccountId!,
                        invoice.TakenById!,
                        invoice.InvoiceNumber!,
                        transaction.PaymentRequestId,
                        transactionHistoryId,
                        ct);

                    if (supplierTeamToContactWithCustomerV2 != null)
                    {
                        var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
                        supplierTeamToContactWithCustomerV2.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
                        supplierNotification.EmailDelivery!.Add(supplierTeamToContactWithCustomerV2);
                        supplierNotification.UserUiReviewDelivery!.Add(new NotificationChannelDto<UserUiReviewPayloadDto>
                        {
                            Payload = new UserUiReviewPayloadDto
                            {
                                NotificationLevel = NotificationLevel.Info,
                                Description = $"Invoice payment failed on {invoice.InvoiceNumber}. Please contact the customer to solve the issue or use other payment options"
                            }
                        });

                        await azureNotificationSenderService.Send(supplierNotification, ct);
                    }
                    break;
                case NotificationReceiverType.Merchant:
                    var supplierTeamAboutErrorDuringMerchantPushTransaction = await emailNotificationService.NotifySupplierTeamAboutErrorDuringMerchantPushTransaction_V2(
                        payerCompany.Name,
                        merchantCompany.Id,
                        invoice.CustomerAccountId!,
                        invoice.TakenById!,
                        invoice.InvoiceNumber!,
                        transaction.PaymentRequestId,
                        transactionHistoryId,
                        ct);

                    if (supplierTeamAboutErrorDuringMerchantPushTransaction != null)
                    {
                        var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
                        supplierTeamAboutErrorDuringMerchantPushTransaction.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
                        supplierNotification.EmailDelivery!.Add(supplierTeamAboutErrorDuringMerchantPushTransaction);
                        supplierNotification.UserUiReviewDelivery!.Add(new NotificationChannelDto<UserUiReviewPayloadDto>
                        {
                            Payload = new UserUiReviewPayloadDto
                            {
                                NotificationLevel = NotificationLevel.Info,
                                Description = $"{payerCompany.Name} payment for invoice {invoice.InvoiceNumber!} has failed during merchant push transaction"
                            }
                        });

                        await azureNotificationSenderService.Send(supplierNotification, ct);
                    }
                    break;
                case NotificationReceiverType.OperationTeam:
                    var opsTeamAboutFailedInternalTransferTransaction = await emailNotificationService.NotifyOpsTeamAboutFailedInternalTransferTransaction_V2(
                        merchantCompany.Name,
                        transaction.PaymentRequestId,
                        transactionHistoryId,
                        ct);

                    if (opsTeamAboutFailedInternalTransferTransaction != null)
                    {
                        var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
                        opsTeamNotification.BlueTapeBackOfficeDelivery!.Add(opsTeamAboutFailedInternalTransferTransaction);
                        await azureNotificationSenderService.Send(opsTeamNotification, ct);
                    }
                    break;
                case NotificationReceiverType.None:
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    public async Task HandlePaymentFailed(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var payableIds = transaction
            .PaymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var drawId = transaction.PaymentRequest?.PaymentRequestDetails?.DrawId;
        var paymentRequest = transaction.PaymentRequest;
        var loanApplication = await loanApplicationRepository.GetByLmsId(drawId.ToString(), ct);

        if (payableIds.IsNullOrEmpty() && paymentRequest!.RequestType == PaymentRequestType.DrawRepayment && drawId.HasValue)
        {
            if (loanApplication != null)
                payableIds = loanApplication.InvoiceDetails.InvoiceIds.ToArray();
        }

        string loanApplicationLink = string.Empty;
        if (loanApplication is not null)
        {
            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            var prefix = env == EnvironmentConstants.Production ? "" : $"{env}-";
            loanApplicationLink = $"https://{prefix}admin.bluetape.com/loan/status/{loanApplication.BlueTapeId}";
        }

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var payableNumbers = invoices.Select(x => x.InvoiceNumber).ToArray();
        var errorReason = transaction.LastResultCode.IsNotNullOrEmpty() ?
            $"{transaction.LastResultCode} {transaction.LastResult}" : "Reason code not received from Aion";

        var payerName = (await companyHttpClient.GetCompaniesByIdsAsync(new[] { transaction.PaymentRequest!.PayerId! }, ct))
            ?.FirstOrDefault()
            ?.Name;

        var opsTeamAboutPaymentFailedV2 = await emailNotificationService.NotifyOpsTeamAboutPaymentFailed_V2(
            payableNumbers!,
            paymentRequest!.Amount,
            errorReason,
            transaction.Status.ToString(),
            transaction.PaymentRequestId,
            transaction.ReferenceNumber,
            drawId,
            payerName.IsNullOrEmpty() ? "N/A" : payerName,
            loanApplicationLink,
            paymentRequest.RequestType,
            transactionHistoryId,
            ct);

        var notificationName = "InvoicePaymentFailed";
        var notificationDescription = $"Invoice payment failed. Reason: {errorReason}";

        var referenceIds = new List<string>
        {
            transaction.PaymentRequestId.ToString(),
        };

        if (invoices.Any())
        {
            referenceIds.AddRange(payableIds);

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }
        }

        if (opsTeamAboutPaymentFailedV2 != null)
        {
            var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
            opsTeamNotification.BlueTapeBackOfficeDelivery!.Add(opsTeamAboutPaymentFailedV2);
            await azureNotificationSenderService.Send(opsTeamNotification, ct);
        }
    }

    public async Task HandleIhcRepaymentProcessing(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var payableIds = transaction
            .PaymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var paymentRequest = transaction.PaymentRequest;

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);

        var payerBankAccountId = transaction?.ReceiverAccountId;
        var bankAccount = await companyHttpClient.GetBankAccountByIdAsync(payerBankAccountId, ct);
        var bankAccountNumber = bankAccount?.AccountNumber?.Display;

        var customerAccountName = string.Empty;

        if (invoices.Any())
            customerAccountName = (await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct))?.Name ?? "";

        var notification = await emailNotificationService.NotifyIhcRepaymentIsProcessing(
            invoices,
            customerAccountName,
            bankAccountNumber,
            paymentRequest!.Amount,
            transaction.Amount,
            paymentRequest.PayerId,
            ct);

        var notificationName = "IhcRepaymentProcessing";
        var notificationDescription = $"Ihc Repayment is Processing";

        var referenceIds = new List<string>
        {
            transaction.PaymentRequestId.ToString(),
        };

        if (invoices.Any())
        {
            referenceIds.AddRange(payableIds);

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }
        }

        if (notification != null)
        {
            var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
            opsTeamNotification.EmailDelivery = [notification];
            await azureNotificationSenderService.Send(opsTeamNotification, ct);
        }
    }

    public async Task HandleIhcRepaymentFailed(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var payableIds = transaction
            .PaymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var paymentRequest = transaction.PaymentRequest;

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var errorReason = transaction.LastResultCode.IsNotNullOrEmpty() ?
            $"{transaction.LastResultCode} {transaction.LastResult}" : "Reason code not received from Aion";

        var payerBankAccountId = transaction?.ReceiverAccountId;
        var bankAccount = await companyHttpClient.GetBankAccountByIdAsync(payerBankAccountId, ct);
        var bankAccountNumber = bankAccount?.AccountNumber?.Display;

        var customerAccountName = string.Empty;

        if (invoices.Any())
            customerAccountName = (await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct))?.Name ?? "";

        var notification = await emailNotificationService.NotifyIhcRepaymentFailed(
            invoices!,
            customerAccountName,
            bankAccountNumber,
            paymentRequest!.Amount,
            errorReason,
            paymentRequest.PayerId,
            ct);

        var notificationName = "IhcRepaymentPaymentFailed";
        var notificationDescription = $"Ihc repayment failed. Reason: {errorReason}";

        var referenceIds = new List<string>
        {
            transaction.PaymentRequestId.ToString(),
        };

        if (invoices.Any())
        {
            referenceIds.AddRange(payableIds);

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }
        }

        if (notification != null)
        {
            var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
            opsTeamNotification.EmailDelivery = [notification];
            await azureNotificationSenderService.Send(opsTeamNotification, ct);
        }
    }
    public async Task HandleIhcRepaymentFailedToOpsTeam(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var payableIds = transaction
            .PaymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var errorReason = transaction.LastResultCode.IsNotNullOrEmpty() ?
            $"{transaction.LastResultCode} {transaction.LastResult}" : "Reason code not received from Aion";

        var customer = await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct);
        var customerAccountName = customer?.Name ?? "";
        var customerEmail = customer?.EmailAddress ?? "";

        var notification = await emailNotificationService.NotifyIhcRepaymentFailedToOpsTeam(
            invoices!,
            customerAccountName,
            transaction.Amount,
            customerEmail,
            errorReason,
            ct);

        var notificationName = "IhcRepaymentPaymentFailed";
        var notificationDescription = $"Ihc repayment failed. Reason: {errorReason}";

        var referenceIds = new List<string>
        {
            transaction.PaymentRequestId.ToString(),
        };

        if (invoices.Any())
        {
            referenceIds.AddRange(payableIds);

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }
        }

        if (notification != null)
        {
            var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
            opsTeamNotification.EmailDelivery = [notification];
            await azureNotificationSenderService.Send(opsTeamNotification, ct);
        }
    }

    public async Task HandleInvoicePaymentRecalled(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var payableIds = transaction
            .PaymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var payableNumbers = invoices.Select(x => x.InvoiceNumber).ToArray();

        var opsTeamAboutRecalledV2 = await emailNotificationService.NotifyOpsTeamAboutRecalled_V2(
            payableNumbers!,
            transaction.PaymentRequest.Amount,
            transaction.PaymentRequestId,
            transactionHistoryId,
            ct);


        var numbers = string.Join(", ", payableNumbers);

        var notificationName = "InvoicePaymentRecalled";
        var notificationDescription = $"The ACH Pull for invoices {numbers} with total amount {transaction.PaymentRequest.Amount.ToDollarString()} is returned.";

        var referenceIds = new List<string>
        {
            transaction.PaymentRequestId.ToString(),
        };

        if (invoices.Any())
        {
            referenceIds.AddRange(payableIds);

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }
        }

        if (opsTeamAboutRecalledV2 != null)
        {
            var opsTeamNotification = GetNotification(notificationName, notificationDescription, referenceIds, string.Empty);
            opsTeamNotification.BlueTapeBackOfficeDelivery!.Add(opsTeamAboutRecalledV2);
            await azureNotificationSenderService.Send(opsTeamNotification, ct);
        }
    }

    public async Task HandleInvoicePaymentCleared(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var paymentRequest = transaction.PaymentRequest;
        var payableIds = paymentRequest!
            .PaymentRequestPayables
            .Select(x => x.PayableId)
            .ToArray();

        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var customerAccountName = (await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct))?.Name ?? "";

        foreach (var payable in paymentRequest.PaymentRequestPayables.ToList())
        {
            var invoice = invoices.Find(x => x.Id == payable.PayableId);

            var paymentIsProcessedV2 = await emailNotificationService.NotifyPaymentIsProcessed_V2(
                [invoice],
                customerAccountName,
                paymentRequest.Amount,
                transaction.Amount,
                paymentRequest.Id,
                paymentRequest.RequestType,
                DisplayHelper.GetPaymentType(paymentRequest.RequestType),
                transaction.Id,
                transactionHistoryId,
                ct
                );

            if (paymentIsProcessedV2 != null)
            {
                var notificationName = "InvoicePaymentCleared";
                var notificationDescription = paymentIsProcessedV2.Payload.Subject;

                var referenceIds = new List<string>
                {
                    transaction.PaymentRequestId.ToString(),
                };

                referenceIds.Add(payable.PayableId);

                if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
                {
                    referenceIds.Add(invoices[0].CustomerAccountId!);
                }

                var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
                paymentIsProcessedV2.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
                supplierNotification.EmailDelivery!.Add(paymentIsProcessedV2);
                supplierNotification.UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>()
                {
                    new()
                    {
                        Payload = new UserUiReviewPayloadDto
                        {
                            NotificationLevel = NotificationLevel.Info,
                            Description = notificationDescription
                        }
                    }
                };
                await azureNotificationSenderService.Send(supplierNotification, ct);
            }
        }
    }

    public async Task HandleAdvancePaymentCleared(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var paymentRequest = transaction.PaymentRequest;
        var payableIds = paymentRequest!.PaymentRequestPayables.Select(x => x.PayableId).ToList();
        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var customerAccountName = (await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct))?.Name ?? "";

        var paymentIsProcessedV2 = await emailNotificationService.NotifyAdvancePaymentIsProcessed_V2(
            invoices.ToArray(),
            customerAccountName,
            paymentRequest.Amount,
            paymentRequest.Id,
            paymentRequest.RequestType,
            DisplayHelper.GetPaymentType(paymentRequest.RequestType),
            transaction.Id,
            transactionHistoryId,
            ct
        );

        if (paymentIsProcessedV2 != null)
        {
            var notificationName = "AdvancePaymentCleared";
            var notificationDescription = paymentIsProcessedV2.Payload.Subject;

            var referenceIds = new List<string>
            {
                transaction.PaymentRequestId.ToString(),
            };

            referenceIds.AddRange(invoices.Select(x => x.Id).Distinct().ToList());

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }

            var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
            paymentIsProcessedV2.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
            supplierNotification.EmailDelivery!.Add(paymentIsProcessedV2);
            supplierNotification.UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>()
            {
                new()
                {
                    Payload = new UserUiReviewPayloadDto
                    {
                        NotificationLevel = NotificationLevel.Info,
                        Description = notificationDescription
                    }
                }
            };
            await azureNotificationSenderService.Send(supplierNotification, ct);
        }
    }

    public async Task HandleFinalPaymentCleared(PaymentTransactionEntity transaction, Guid transactionHistoryId, CancellationToken ct)
    {
        var paymentRequest = transaction.PaymentRequest;
        var payableIds = paymentRequest!.PaymentRequestPayables.Select(x => x.PayableId).ToList();
        var invoices = await invoiceRepository.GetByIds(payableIds, ct);
        var customerAccountName = (await customerAccountsRepository.GetByIdAsync(invoices.First().CustomerAccountId!, ct))?.Name ?? "";
        var fee = await operationsService.GetTotalMerchantFee(invoices, ct);

        var finalPaymentIsSuccessV2 = await emailNotificationService.NotifyFinalPaymentIsSuccess_V2(
            invoices.ToArray(),
            customerAccountName,
            paymentRequest.Id,
            paymentRequest.Amount,
            DisplayHelper.GetPaymentType(paymentRequest.RequestType),
            transactionHistoryId,
            transaction.ReferenceNumber,
            fee,
            ct
        );

        if (finalPaymentIsSuccessV2 != null)
        {
            var notificationName = "FinalPaymentCleared";
            var notificationDescription = finalPaymentIsSuccessV2.Payload.Subject;

            var referenceIds = new List<string>
            {
                transaction.PaymentRequestId.ToString(),
            };

            referenceIds.AddRange(invoices.Select(x => x.Id).Distinct().ToList());

            if (!string.IsNullOrEmpty(invoices[0].CustomerAccountId))
            {
                referenceIds.Add(invoices[0].CustomerAccountId!);
            }

            var supplierNotification = GetNotification(notificationName, notificationDescription, referenceIds, transaction.PaymentRequest.PayeeId);
            finalPaymentIsSuccessV2.Payload.SenderCompanyId = transaction.PaymentRequest.PayeeId;
            supplierNotification.EmailDelivery!.Add(finalPaymentIsSuccessV2);
            supplierNotification.UserUiReviewDelivery = new List<NotificationChannelDto<UserUiReviewPayloadDto>>()
            {
                new()
                {
                    Payload = new UserUiReviewPayloadDto
                    {
                        NotificationLevel = NotificationLevel.Info,
                        Description = notificationDescription
                    }
                }
            };
            await azureNotificationSenderService.Send(supplierNotification, ct);
        }
    }
}