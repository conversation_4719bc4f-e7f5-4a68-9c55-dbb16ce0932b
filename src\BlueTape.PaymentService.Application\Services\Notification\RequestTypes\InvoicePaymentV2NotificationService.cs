using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.Notification.RequestTypes;

public class InvoicePaymentV2NotificationService(
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    IPaymentTransactionRepository paymentTransactionRepository,
    ITransactionNotificationService transactionNotificationService,
    ILoggerFactory loggerFactory) : BaseNotificationService(paymentTransactionHistoryRepository, loggerFactory)
{
    private readonly ILogger _logger = loggerFactory.CreateLogger<InvoicePaymentNotificationService>();

    public override async Task ProcessTransactionUpdate(Guid transactionId, Guid transactionHistoryId, CancellationToken ct)
    {
        var transaction = await paymentTransactionRepository.GetByIdFull(transactionId, ct);
        if (transaction is null) throw new TransactionDoesNotExistException(transactionId);

        var targetHistoryItem = transaction.TransactionHistories.First(x => x.Id == transactionHistoryId);

        switch (targetHistoryItem.NewStatus)
        {
            case TransactionStatus.Error:
                break;
            case TransactionStatus.Failed:
                await transactionNotificationService.HandleInvoicePaymentFailed(transaction, transactionHistoryId, ct);
                await transactionNotificationService.HandlePaymentFailed(transaction, transactionHistoryId, ct);
                break;
            case TransactionStatus.Recalled:
                await transactionNotificationService.HandlePaymentFailed(transaction, transactionHistoryId, ct);
                break;
            case TransactionStatus.Placed:
            case TransactionStatus.Processing:
            case TransactionStatus.Processed:
            case TransactionStatus.Canceled:
            case TransactionStatus.Scheduled:
            case TransactionStatus.Aborted:
            case TransactionStatus.Hold:
                break;
            case TransactionStatus.Cleared:
                if (transaction.PaymentRequest is { Status: PaymentRequestStatus.Settled })
                {
                    await transactionNotificationService.HandleInvoicePaymentCleared(transaction, transactionHistoryId,
                        ct);
                }
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public override async Task ProcessPaymentRequestCreated(Guid paymentRequestId, CancellationToken ct)
    {
        await transactionNotificationService.HandleInvoicePaymentRequestCreated(paymentRequestId, ct);
    }
}