using BlueTape.PaymentService.DataAccess.Constants;
using BlueTape.PaymentService.DataAccess.Helpers;
using BlueTape.PaymentService.DataAccess.Interceptors;
using BlueTape.PaymentService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace BlueTape.PaymentService.DataAccess.Contexts;

public sealed class DatabaseContext : DbContext
{
    public DbSet<PaymentRequestEntity>? PaymentRequests { get; set; }
    public DbSet<PaymentRequestCommandEntity>? PaymentRequestCommands { get; set; }
    public DbSet<PaymentRequestPayableEntity>? PaymentRequestPayables { get; set; }
    public DbSet<PaymentTransactionEntity>? PaymentTransactions { get; set; }
    public DbSet<PaymentTransactionHistoryEntity>? PaymentTransactionsHistory { get; set; }
    public DbSet<PaymentRequestFeeEntity>? PaymentRequestFees { get; set; }
    public DbSet<PaymentRequestDetailsEntity>? PaymentRequestDetails { get; set; }
    public DbSet<ForbiddenCompanyEntity>? ForbiddenCompanies { get; set; }
    public DbSet<EventLogEntity>? EventLogs { get; set; }
    public DbSet<PaymentConfigEntity>? PaymentConfigs { get; set; }

    public DatabaseContext(DbContextOptions<DatabaseContext> options) : base(options)
    {
        if (Database.IsRelational() && !Database.IsSqlite())
        {
            Database.Migrate();
        }
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.AddInterceptors(new EntityDateTrackingInterceptor());

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<PaymentRequestDetailsEntity>()
            .HasOne(prd => prd.PaymentRequest)
            .WithOne(pr => pr.PaymentRequestDetails)
            .HasForeignKey<PaymentRequestDetailsEntity>(prd => prd.PaymentRequestId);

        modelBuilder.Entity<PaymentTransactionEntity>()
            .HasIndex(u => u.TransactionNumber)
            .IsUnique();

        if (!Database.IsSqlite())
            modelBuilder.HasSequence<long>(AuthorConstants.PublicTransactionIdentifierSequenceName)
                .HasMin(0)
                .StartsAt(0)
                .IncrementsBy(1);
    }
}