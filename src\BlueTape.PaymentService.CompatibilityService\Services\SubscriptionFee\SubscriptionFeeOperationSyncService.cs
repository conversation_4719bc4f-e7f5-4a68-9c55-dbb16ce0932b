using AutoMapper;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Senders;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Services.Base;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;

public class SubscriptionFeeOperationSyncService(
    IOperationsRepository operationsRepository,
    ISubscriptionFeeCompatibilityMapper compatibilityMapper,
    ISubscriptionFeeTransactionSyncService transactionSyncService,
    IMapper mapper,
    IInvoiceSyncMessageSender messageSender,
    ILogger<SubscriptionFeeOperationSyncService> logger)
    : BaseOperationSyncService(operationsRepository, mapper, messageSender, logger), ISubscriptionFeeOperationSyncService
{
    public async Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        logger.LogInformation("Performing subscription fee operation for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);

        // Create subscription fee operation
        var operation = compatibilityMapper.MapFromPaymentRequestToSubscriptionFeeOperation(paymentRequest);
        await operationsRepository.InsertMany(new List<OperationEntity> { operation }, cancellationToken);

        // Create transactions for the operation
        await transactionSyncService.PerformTransactions(paymentRequest, operation, cancellationToken);

        logger.LogInformation("Subscription fee operation created successfully for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
    }

    public async Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        logger.LogInformation("Syncing subscription fee operation for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);

        // Find existing operations for this payment request
        var existingOperations = await operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken);

        foreach (var operation in existingOperations)
        {
            if (operation.Status != OperationStatus.FAIL.ToString())
            {
                var updateOperationEntity = GetUpdateOperationEntity(paymentRequest);
                await operationsRepository.UpdateById(operation.BlueTapeId, updateOperationEntity, cancellationToken);
            }
        }

        // Sync transactions
        await transactionSyncService.SyncTransactions(paymentRequest, existingOperations.Select(x => x.BlueTapeId).ToList(), cancellationToken);

        // Sync invoice payment status if payment is settled
        if (paymentRequest.Status == PaymentRequestStatus.Settled)
        {
            await SyncInvoicePaymentData(paymentRequest, cancellationToken);
        }

        logger.LogInformation("Subscription fee operation synced successfully for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
    }

    private new UpdateOperationEntity GetUpdateOperationEntity(PaymentRequestEntity paymentRequest)
    {
        var pullResult = paymentRequest.Transactions.FirstOrDefault(x => x.Status == TransactionStatus.Cleared)?.UpdatedAt;
        var updateOperationEntity = new UpdateOperationEntity()
        {
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Status = paymentRequest.GetPayNowOperationStatus().ToString(),
            PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
            PayerId = paymentRequest.PayerId,
            PayeeId = paymentRequest.PayeeId,
            FirstTransactionDate = paymentRequest.GetFirstTransactionDate(),
            PaymentRequestId = paymentRequest.Id.ToString(),
            PullResult = pullResult,
        };

        return updateOperationEntity;
    }
}
