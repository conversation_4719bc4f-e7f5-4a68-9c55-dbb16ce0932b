using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;

public class SubscriptionFeeTransactionSyncService(
    ISubscriptionFeeCompatibilityMapper compatibilityMapper,
    ITransactionsRepository transactionsRepository,
    ILogger<SubscriptionFeeTransactionSyncService> logger) : ISubscriptionFeeTransactionSyncService
{
    public async Task PerformTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken)
    {
        logger.LogInformation("Creating subscription fee transaction for PaymentRequestId: {PaymentRequestId}, OperationId: {OperationId}",
            paymentRequest.Id, operation.BlueTapeId);

        var legacyTransaction = await compatibilityMapper.MapFromPaymentTransactionToSubscriptionFeeLegacyTransaction(paymentRequest, operation, cancellationToken);
        await transactionsRepository.InsertMany(new List<TransactionEntity> { legacyTransaction }, cancellationToken);

        logger.LogInformation("Subscription fee transaction created successfully for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
    }

    public async Task SyncTransactions(PaymentRequestEntity paymentRequest, List<string> operationIds, CancellationToken cancellationToken)
    {
        logger.LogInformation("Syncing subscription fee transactions for PaymentRequestId: {PaymentRequestId}, OperationIds: {OperationIds}",
            paymentRequest.Id, string.Join(",", operationIds));

        if (!operationIds.Any())
        {
            logger.LogWarning("No operation IDs provided for transaction sync for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
            return;
        }

        // Get existing transactions for the operations
        var transactions = await transactionsRepository.GetByOperationIds(operationIds, LegacyTransactionType.PULL.ToString(), cancellationToken);

        // Get transactions that need to be synced
        var transactionsToSync = paymentRequest.GetTransactionsForSync()
            .Where(x => x.TransactionType == PaymentTransactionType.AchPull) // Subscription fees are typically ACH pulls
            .ToList();

        if (!transactionsToSync.Any())
        {
            logger.LogInformation("No transactions to sync for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
            return;
        }

        // Create sync models for the transactions
        var syncModels = transactionsToSync.Select(compatibilityMapper.MapPaymentTransactionToSyncModel).ToList();
        var updateTransactionEntities = await MapSyncModelsToEntities(syncModels, cancellationToken);

        // Update each transaction with the sync data
        await transactions.ForEachAsync(async transaction =>
        {
            var transactionUpdateEntity = updateTransactionEntities.Find(x => x.PaymentTransactionId == transaction.PaymentTransactionId);

            if (transactionUpdateEntity != null)
            {
                await transactionsRepository.UpdateById(transaction.Id, transactionUpdateEntity, cancellationToken);
                logger.LogDebug("Updated transaction {TransactionId} for PaymentRequestId: {PaymentRequestId}",
                    transaction.Id, paymentRequest.Id);
            }
        }, cancellationToken);

        logger.LogInformation("Subscription fee transactions synced successfully for PaymentRequestId: {PaymentRequestId}", paymentRequest.Id);
    }

    private async Task<List<UpdateTransactionEntity>> MapSyncModelsToEntities(IEnumerable<SyncTransactionModel> syncTransactionModels, CancellationToken cancellationToken)
    {
        var updateTransactionEntities = new List<UpdateTransactionEntity>();

        await syncTransactionModels.ForEachAsync(async transactionToSync =>
        {
            var entity = await compatibilityMapper.MapSyncModelToUpdateTransactionEntity(transactionToSync, cancellationToken);
            updateTransactionEntities.Add(entity);
        }, cancellationToken);

        return updateTransactionEntities;
    }
}
